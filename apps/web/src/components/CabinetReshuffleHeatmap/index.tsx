import React from 'react';
import { Card, Title, Text, Group, Badge, Stack } from '@mantine/core';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import { useTranslation } from 'react-i18next';

interface ReshuffleData {
  departmentId: number;
  departmentName: string;
  reshuffleCount: number;
  avgTenure: number;
}

interface CabinetReshuffleHeatmapProps {
  data: ReshuffleData[];
}

const CustomTooltip = ({ active, payload, label }: any) => {
  const { t } = useTranslation();

  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <Card shadow="md" padding="sm" radius="md" withBorder>
        <Stack gap="xs">
          <Text fw={500} size="sm">
            {data.departmentName}
          </Text>
          <Text size="sm">
            <strong>{data.reshuffleCount}</strong> {t('common:total_appointments', { defaultValue: 'total appointments' })}
          </Text>
          <Text size="sm">
            <strong>{Math.round(data.avgTenure)}</strong> {t('common:days_avg_tenure', { defaultValue: 'days average tenure' })}
          </Text>
        </Stack>
      </Card>
    );
  }

  return null;
};

const getBarColor = (value: number, maxValue: number) => {
  const intensity = value / maxValue;
  if (intensity > 0.8) return '#dc2626'; // Red for high turnover
  if (intensity > 0.6) return '#ea580c'; // Orange-red
  if (intensity > 0.4) return '#f59e0b'; // Orange
  if (intensity > 0.2) return '#eab308'; // Yellow
  return '#22c55e'; // Green for low turnover
};

const CabinetReshuffleHeatmap: React.FC<CabinetReshuffleHeatmapProps> = ({ data }) => {
  const { t } = useTranslation();

  const sortedData = React.useMemo(() => {
    return [...data].sort((a, b) => b.reshuffleCount - a.reshuffleCount);
  }, [data]);

  const maxReshuffles = Math.max(...data.map(d => d.reshuffleCount));
  const avgReshuffles = data.reduce((sum, d) => sum + d.reshuffleCount, 0) / data.length;

  const topDepartments = sortedData.slice(0, 5);
  const stableDepartments = sortedData.slice(-5).reverse();

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={3}>
            {t('common:cabinet_reshuffle_analysis', { defaultValue: 'Cabinet Reshuffle Analysis' })}
          </Title>
          <Text c="dimmed" size="sm">
            {t('common:ministry_turnover_rates', { 
              defaultValue: 'Ministry turnover rates and appointment frequency' 
            })}
          </Text>
        </div>
        <Group gap="xs">
          <Badge variant="light" color="red">
            {t('common:high_turnover', { defaultValue: 'High Turnover' })}
          </Badge>
          <Badge variant="light" color="green">
            {t('common:stable', { defaultValue: 'Stable' })}
          </Badge>
        </Group>
      </Group>

      <ResponsiveContainer width="100%" height={400}>
        <BarChart
          data={sortedData}
          margin={{ top: 20, right: 30, left: 20, bottom: 100 }}
        >
          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
          <XAxis 
            dataKey="departmentName"
            angle={-45}
            textAnchor="end"
            height={120}
            fontSize={11}
            interval={0}
          />
          <YAxis 
            label={{ 
              value: t('common:number_of_appointments', { defaultValue: 'Number of Appointments' }), 
              angle: -90, 
              position: 'insideLeft' 
            }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="reshuffleCount" radius={[4, 4, 0, 0]}>
            {sortedData.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={getBarColor(entry.reshuffleCount, maxReshuffles)} 
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>

      <Group justify="space-around" mt="xl">
        <Stack gap="xs" align="center">
          <Text fw={500} size="lg" c="red">
            {maxReshuffles}
          </Text>
          <Text size="xs" c="dimmed" ta="center">
            {t('common:highest_turnover', { defaultValue: 'Highest Turnover' })}
          </Text>
          <Text size="xs" c="dimmed" ta="center">
            {topDepartments[0]?.departmentName}
          </Text>
        </Stack>

        <Stack gap="xs" align="center">
          <Text fw={500} size="lg" c="blue">
            {Math.round(avgReshuffles)}
          </Text>
          <Text size="xs" c="dimmed" ta="center">
            {t('common:average_turnover', { defaultValue: 'Average Turnover' })}
          </Text>
        </Stack>

        <Stack gap="xs" align="center">
          <Text fw={500} size="lg" c="green">
            {stableDepartments[0]?.reshuffleCount || 0}
          </Text>
          <Text size="xs" c="dimmed" ta="center">
            {t('common:most_stable', { defaultValue: 'Most Stable' })}
          </Text>
          <Text size="xs" c="dimmed" ta="center">
            {stableDepartments[0]?.departmentName}
          </Text>
        </Stack>
      </Group>

      <Group justify="space-between" mt="xl">
        <Stack gap="xs" style={{ flex: 1 }}>
          <Text fw={500} size="sm" c="red">
            {t('common:highest_turnover_ministries', { defaultValue: 'Highest Turnover Ministries' })}
          </Text>
          {topDepartments.map((dept, index) => (
            <Group key={dept.departmentId} justify="space-between">
              <Text size="xs" lineClamp={1} style={{ flex: 1 }}>
                {index + 1}. {dept.departmentName}
              </Text>
              <Badge size="xs" color="red" variant="light">
                {dept.reshuffleCount}
              </Badge>
            </Group>
          ))}
        </Stack>

        <Stack gap="xs" style={{ flex: 1 }}>
          <Text fw={500} size="sm" c="green">
            {t('common:most_stable_ministries', { defaultValue: 'Most Stable Ministries' })}
          </Text>
          {stableDepartments.map((dept, index) => (
            <Group key={dept.departmentId} justify="space-between">
              <Text size="xs" lineClamp={1} style={{ flex: 1 }}>
                {index + 1}. {dept.departmentName}
              </Text>
              <Badge size="xs" color="green" variant="light">
                {dept.reshuffleCount}
              </Badge>
            </Group>
          ))}
        </Stack>
      </Group>
    </Card>
  );
};

export default CabinetReshuffleHeatmap;
