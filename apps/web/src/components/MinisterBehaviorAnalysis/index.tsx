import React, { useState } from 'react';
import { 
  Card, 
  Title, 
  Text, 
  Group, 
  Avatar, 
  Badge, 
  Table, 
  Tabs, 
  Stack,
  ActionIcon,
  Tooltip,
  SimpleGrid
} from '@mantine/core';
import { IconArrowUp, IconArrowDown, IconSwitchHorizontal, IconCrown, IconCalendar } from '@tabler/icons-react';
import { useTranslation } from 'react-i18next';
import { getImageUrlWithFallback } from '@/utils';
import Link from 'next/link';

interface RecurringMinister {
  leaderId: number;
  leaderName: string;
  leaderLocalName: string;
  leaderImg: string | null;
  appearances: number;
  governments: string;
  totalTenure: number;
}

interface PartySwitcher {
  leaderId: number;
  leaderName: string;
  leaderLocalName: string;
  leaderImg: string | null;
  partyChanges: number;
  parties: string;
}

interface MinisterBehaviorData {
  recurringMinisters: RecurringMinister[];
  partySwitchers: PartySwitcher[];
}

interface MinisterBehaviorAnalysisProps {
  data: MinisterBehaviorData;
}

const MinisterCard = ({ minister, type }: { minister: RecurringMinister | PartySwitcher, type: 'recurring' | 'switcher' }) => {
  const { t } = useTranslation();
  
  return (
    <Card shadow="xs" padding="md" radius="md" withBorder>
      <Group gap="md">
        <Avatar
          src={getImageUrlWithFallback(minister.leaderImg, minister.leaderId.toString())}
          size="lg"
          radius="xl"
        />
        <Stack gap="xs" style={{ flex: 1 }}>
          <Link href={`/leaders/${minister.leaderId}`} className="no-underline">
            <Text fw={500} size="sm" c="blue">
              {minister.leaderLocalName || minister.leaderName}
            </Text>
          </Link>
          
          {type === 'recurring' && (
            <>
              <Group gap="xs">
                <IconCrown size={16} />
                <Text size="xs" c="dimmed">
                  {(minister as RecurringMinister).appearances} {t('common:governments', { defaultValue: 'governments' })}
                </Text>
              </Group>
              <Group gap="xs">
                <IconCalendar size={16} />
                <Text size="xs" c="dimmed">
                  {Math.round((minister as RecurringMinister).totalTenure / 365)} {t('common:years_total', { defaultValue: 'years total' })}
                </Text>
              </Group>
            </>
          )}
          
          {type === 'switcher' && (
            <>
              <Group gap="xs">
                <IconSwitchHorizontal size={16} />
                <Text size="xs" c="dimmed">
                  {(minister as PartySwitcher).partyChanges} {t('common:party_changes', { defaultValue: 'party changes' })}
                </Text>
              </Group>
              <Text size="xs" c="dimmed" lineClamp={2}>
                {(minister as PartySwitcher).parties}
              </Text>
            </>
          )}
        </Stack>
        
        <Badge 
          color={type === 'recurring' ? 'blue' : 'orange'} 
          variant="light"
          size="lg"
        >
          {type === 'recurring' 
            ? (minister as RecurringMinister).appearances 
            : (minister as PartySwitcher).partyChanges
          }
        </Badge>
      </Group>
    </Card>
  );
};

const SortableTable = ({ data, type }: { data: RecurringMinister[] | PartySwitcher[], type: 'recurring' | 'switcher' }) => {
  const { t } = useTranslation();
  const [sortBy, setSortBy] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const sortedData = React.useMemo(() => {
    if (!sortBy) return data;
    
    return [...data].sort((a, b) => {
      const aVal = (a as any)[sortBy];
      const bVal = (b as any)[sortBy];
      
      if (typeof aVal === 'string') {
        return sortOrder === 'asc' 
          ? aVal.localeCompare(bVal)
          : bVal.localeCompare(aVal);
      }
      
      return sortOrder === 'asc' ? aVal - bVal : bVal - aVal;
    });
  }, [data, sortBy, sortOrder]);

  const SortIcon = ({ field }: { field: string }) => (
    <ActionIcon
      variant="transparent"
      size="xs"
      onClick={() => handleSort(field)}
    >
      {sortBy === field ? (
        sortOrder === 'asc' ? <IconArrowUp size={12} /> : <IconArrowDown size={12} />
      ) : (
        <IconArrowDown size={12} opacity={0.3} />
      )}
    </ActionIcon>
  );

  return (
    <Table striped highlightOnHover>
      <Table.Thead>
        <Table.Tr>
          <Table.Th>
            <Group gap="xs">
              {t('common:minister', { defaultValue: 'Minister' })}
              <SortIcon field="leaderLocalName" />
            </Group>
          </Table.Th>
          {type === 'recurring' ? (
            <>
              <Table.Th>
                <Group gap="xs">
                  {t('common:appearances', { defaultValue: 'Appearances' })}
                  <SortIcon field="appearances" />
                </Group>
              </Table.Th>
              <Table.Th>
                <Group gap="xs">
                  {t('common:total_tenure', { defaultValue: 'Total Tenure' })}
                  <SortIcon field="totalTenure" />
                </Group>
              </Table.Th>
              <Table.Th>{t('common:governments', { defaultValue: 'Governments' })}</Table.Th>
            </>
          ) : (
            <>
              <Table.Th>
                <Group gap="xs">
                  {t('common:party_changes', { defaultValue: 'Party Changes' })}
                  <SortIcon field="partyChanges" />
                </Group>
              </Table.Th>
              <Table.Th>{t('common:parties', { defaultValue: 'Parties' })}</Table.Th>
            </>
          )}
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>
        {sortedData.map((minister) => (
          <Table.Tr key={minister.leaderId}>
            <Table.Td>
              <Group gap="sm">
                <Avatar
                  src={getImageUrlWithFallback(minister.leaderImg, minister.leaderId.toString())}
                  size="sm"
                  radius="xl"
                />
                <Link href={`/leaders/${minister.leaderId}`} className="no-underline">
                  <Text size="sm" c="blue">
                    {minister.leaderLocalName || minister.leaderName}
                  </Text>
                </Link>
              </Group>
            </Table.Td>
            {type === 'recurring' ? (
              <>
                <Table.Td>
                  <Badge color="blue" variant="light">
                    {(minister as RecurringMinister).appearances}
                  </Badge>
                </Table.Td>
                <Table.Td>
                  <Text size="sm">
                    {Math.round((minister as RecurringMinister).totalTenure / 365)} {t('common:years', { defaultValue: 'years' })}
                  </Text>
                </Table.Td>
                <Table.Td>
                  <Text size="xs" c="dimmed" lineClamp={2}>
                    {(minister as RecurringMinister).governments}
                  </Text>
                </Table.Td>
              </>
            ) : (
              <>
                <Table.Td>
                  <Badge color="orange" variant="light">
                    {(minister as PartySwitcher).partyChanges}
                  </Badge>
                </Table.Td>
                <Table.Td>
                  <Text size="xs" c="dimmed" lineClamp={2}>
                    {(minister as PartySwitcher).parties}
                  </Text>
                </Table.Td>
              </>
            )}
          </Table.Tr>
        ))}
      </Table.Tbody>
    </Table>
  );
};

const MinisterBehaviorAnalysis: React.FC<MinisterBehaviorAnalysisProps> = ({ data }) => {
  const { t } = useTranslation();

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Title order={3} mb="md">
        {t('common:minister_behavior', { defaultValue: 'Minister Behavior Analysis' })}
      </Title>
      
      <Tabs defaultValue="recurring" variant="outline">
        <Tabs.List>
          <Tabs.Tab value="recurring">
            {t('common:recurring_ministers', { defaultValue: 'Recurring Ministers' })}
            <Badge ml="xs" size="sm" variant="light" color="blue">
              {data.recurringMinisters.length}
            </Badge>
          </Tabs.Tab>
          <Tabs.Tab value="switchers">
            {t('common:party_switchers', { defaultValue: 'Party Switchers' })}
            <Badge ml="xs" size="sm" variant="light" color="orange">
              {data.partySwitchers.length}
            </Badge>
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="recurring" pt="md">
          <Stack gap="md">
            <Text c="dimmed" size="sm">
              {t('common:recurring_ministers_desc', { 
                defaultValue: 'Ministers who have served in multiple governments' 
              })}
            </Text>
            
            <SimpleGrid cols={{ base: 1, md: 2, lg: 3 }} spacing="md">
              {data.recurringMinisters.slice(0, 6).map((minister) => (
                <MinisterCard key={minister.leaderId} minister={minister} type="recurring" />
              ))}
            </SimpleGrid>
            
            {data.recurringMinisters.length > 6 && (
              <SortableTable data={data.recurringMinisters} type="recurring" />
            )}
          </Stack>
        </Tabs.Panel>

        <Tabs.Panel value="switchers" pt="md">
          <Stack gap="md">
            <Text c="dimmed" size="sm">
              {t('common:party_switchers_desc', { 
                defaultValue: 'Ministers who have changed parties between governments' 
              })}
            </Text>
            
            <SimpleGrid cols={{ base: 1, md: 2, lg: 3 }} spacing="md">
              {data.partySwitchers.slice(0, 6).map((minister) => (
                <MinisterCard key={minister.leaderId} minister={minister} type="switcher" />
              ))}
            </SimpleGrid>
            
            {data.partySwitchers.length > 6 && (
              <SortableTable data={data.partySwitchers} type="switcher" />
            )}
          </Stack>
        </Tabs.Panel>
      </Tabs>
    </Card>
  );
};

export default MinisterBehaviorAnalysis;
