import React from 'react';
import { Card, Title, Text, Group, Badge, Stack, Avatar, Box, Tooltip } from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { getImageUrlWithFallback } from '@/utils';
import Link from 'next/link';

interface PartyTimelineData {
  partyId: number;
  partyName: string;
  partyCode: string;
  partyLogo: string | null;
  partyColorCode: string | null;
  firstEntry: Date;
  lastExit: Date | null;
  totalGovernments: number;
  totalMinisters: number;
}

interface PartyEntryExitTimelineProps {
  data: PartyTimelineData[];
}

const PartyTimelineBar = ({ party, minYear, maxYear }: { 
  party: PartyTimelineData; 
  minYear: number; 
  maxYear: number; 
}) => {
  const { t } = useTranslation();
  
  const startYear = new Date(party.firstEntry).getFullYear();
  const endYear = party.lastExit ? new Date(party.lastExit).getFullYear() : new Date().getFullYear();
  
  const totalSpan = maxYear - minYear;
  const partySpan = endYear - startYear;
  const startOffset = ((startYear - minYear) / totalSpan) * 100;
  const width = (partySpan / totalSpan) * 100;
  
  const isActive = !party.lastExit;
  
  return (
    <Group gap="md" mb="md" align="center">
      <Group gap="sm" style={{ minWidth: '200px' }}>
        <Avatar
          src={getImageUrlWithFallback(party.partyLogo, party.partyCode)}
          size="sm"
          radius="xl"
        />
        <Stack gap={0}>
          <Link href={`/parties/${party.partyId}`} className="no-underline">
            <Text size="sm" fw={500} c="blue" lineClamp={1}>
              {party.partyName}
            </Text>
          </Link>
          <Text size="xs" c="dimmed">
            {party.totalGovernments} {t('common:governments', { defaultValue: 'governments' })}
          </Text>
        </Stack>
      </Group>
      
      <Box style={{ flex: 1, position: 'relative', height: '24px' }}>
        {/* Background timeline */}
        <Box
          style={{
            position: 'absolute',
            top: '8px',
            left: 0,
            right: 0,
            height: '8px',
            backgroundColor: '#f1f3f4',
            borderRadius: '4px',
          }}
        />
        
        {/* Party timeline bar */}
        <Tooltip
          label={
            <Stack gap="xs">
              <Text size="sm" fw={500}>{party.partyName}</Text>
              <Text size="xs">
                {t('common:first_entry', { defaultValue: 'First Entry' })}: {startYear}
              </Text>
              <Text size="xs">
                {t('common:last_exit', { defaultValue: 'Last Exit' })}: {endYear}
              </Text>
              <Text size="xs">
                {party.totalMinisters} {t('common:total_ministers', { defaultValue: 'total ministers' })}
              </Text>
            </Stack>
          }
          position="top"
        >
          <Box
            style={{
              position: 'absolute',
              top: '8px',
              left: `${startOffset}%`,
              width: `${Math.max(width, 2)}%`,
              height: '8px',
              backgroundColor: party.partyColorCode || '#3b82f6',
              borderRadius: '4px',
              cursor: 'pointer',
              opacity: isActive ? 1 : 0.7,
              border: isActive ? '2px solid #22c55e' : 'none',
            }}
          />
        </Tooltip>
        
        {/* Start marker */}
        <Box
          style={{
            position: 'absolute',
            top: '4px',
            left: `${startOffset}%`,
            width: '16px',
            height: '16px',
            backgroundColor: party.partyColorCode || '#3b82f6',
            borderRadius: '50%',
            border: '2px solid white',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            transform: 'translateX(-50%)',
          }}
        />
        
        {/* End marker (if party has exited) */}
        {party.lastExit && (
          <Box
            style={{
              position: 'absolute',
              top: '4px',
              left: `${startOffset + width}%`,
              width: '16px',
              height: '16px',
              backgroundColor: '#ef4444',
              borderRadius: '50%',
              border: '2px solid white',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              transform: 'translateX(-50%)',
            }}
          />
        )}
      </Box>
      
      <Group gap="xs" style={{ minWidth: '120px' }}>
        <Badge size="sm" variant="light" color={isActive ? 'green' : 'gray'}>
          {startYear} - {isActive ? t('common:present', { defaultValue: 'Present' }) : endYear}
        </Badge>
      </Group>
    </Group>
  );
};

const YearMarkers = ({ minYear, maxYear }: { minYear: number; maxYear: number }) => {
  const years = [];
  const step = Math.ceil((maxYear - minYear) / 10); // Show about 10 markers
  
  for (let year = minYear; year <= maxYear; year += step) {
    years.push(year);
  }
  
  return (
    <Box style={{ position: 'relative', height: '30px', marginBottom: '20px' }}>
      {years.map((year) => {
        const position = ((year - minYear) / (maxYear - minYear)) * 100;
        return (
          <Box
            key={year}
            style={{
              position: 'absolute',
              left: `${position}%`,
              transform: 'translateX(-50%)',
              textAlign: 'center',
            }}
          >
            <Box
              style={{
                width: '1px',
                height: '10px',
                backgroundColor: '#d1d5db',
                margin: '0 auto',
              }}
            />
            <Text size="xs" c="dimmed" mt="xs">
              {year}
            </Text>
          </Box>
        );
      })}
    </Box>
  );
};

const PartyEntryExitTimeline: React.FC<PartyEntryExitTimelineProps> = ({ data }) => {
  const { t } = useTranslation();

  const sortedData = React.useMemo(() => {
    return [...data].sort((a, b) => new Date(a.firstEntry).getTime() - new Date(b.firstEntry).getTime());
  }, [data]);

  const { minYear, maxYear } = React.useMemo(() => {
    const years = data.flatMap(party => [
      new Date(party.firstEntry).getFullYear(),
      party.lastExit ? new Date(party.lastExit).getFullYear() : new Date().getFullYear(),
    ]);
    
    return {
      minYear: Math.min(...years),
      maxYear: Math.max(...years),
    };
  }, [data]);

  const activeParties = data.filter(party => !party.lastExit);
  const exitedParties = data.filter(party => party.lastExit);

  const stats = React.useMemo(() => {
    const totalMinisters = data.reduce((sum, party) => sum + party.totalMinisters, 0);
    const avgGovernments = data.reduce((sum, party) => sum + party.totalGovernments, 0) / data.length;
    
    return {
      totalParties: data.length,
      activeParties: activeParties.length,
      exitedParties: exitedParties.length,
      totalMinisters,
      avgGovernments: Math.round(avgGovernments * 10) / 10,
    };
  }, [data, activeParties, exitedParties]);

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={3}>
            {t('common:party_government_timeline', { defaultValue: 'Party Government Timeline' })}
          </Title>
          <Text c="dimmed" size="sm">
            {t('common:party_entry_exit_patterns', { 
              defaultValue: 'When parties first entered and last exited government' 
            })}
          </Text>
        </div>
        <Group gap="xs">
          <Badge variant="light" color="green">
            {activeParties.length} {t('common:active', { defaultValue: 'Active' })}
          </Badge>
          <Badge variant="light" color="gray">
            {exitedParties.length} {t('common:exited', { defaultValue: 'Exited' })}
          </Badge>
        </Group>
      </Group>

      {/* Statistics */}
      <Group justify="space-around" mb="xl" p="md" style={{ backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
        <Stack gap="xs" align="center">
          <Text fw={500} size="lg" c="blue">
            {stats.totalParties}
          </Text>
          <Text size="xs" c="dimmed" ta="center">
            {t('common:total_parties', { defaultValue: 'Total Parties' })}
          </Text>
        </Stack>
        
        <Stack gap="xs" align="center">
          <Text fw={500} size="lg" c="green">
            {stats.activeParties}
          </Text>
          <Text size="xs" c="dimmed" ta="center">
            {t('common:currently_active', { defaultValue: 'Currently Active' })}
          </Text>
        </Stack>
        
        <Stack gap="xs" align="center">
          <Text fw={500} size="lg" c="orange">
            {stats.avgGovernments}
          </Text>
          <Text size="xs" c="dimmed" ta="center">
            {t('common:avg_governments', { defaultValue: 'Avg Governments' })}
          </Text>
        </Stack>
        
        <Stack gap="xs" align="center">
          <Text fw={500} size="lg" c="purple">
            {stats.totalMinisters}
          </Text>
          <Text size="xs" c="dimmed" ta="center">
            {t('common:total_ministers', { defaultValue: 'Total Ministers' })}
          </Text>
        </Stack>
      </Group>

      {/* Timeline */}
      <Stack>
        <YearMarkers minYear={minYear} maxYear={maxYear} />
        
        <Stack gap="sm">
          {sortedData.map((party) => (
            <PartyTimelineBar
              key={party.partyId}
              party={party}
              minYear={minYear}
              maxYear={maxYear}
            />
          ))}
        </Stack>
      </Stack>

      {/* Legend */}
      <Group justify="center" mt="xl" gap="xl">
        <Group gap="xs">
          <Box
            style={{
              width: '16px',
              height: '16px',
              backgroundColor: '#3b82f6',
              borderRadius: '50%',
              border: '2px solid white',
            }}
          />
          <Text size="sm">{t('common:first_entry', { defaultValue: 'First Entry' })}</Text>
        </Group>
        
        <Group gap="xs">
          <Box
            style={{
              width: '16px',
              height: '16px',
              backgroundColor: '#ef4444',
              borderRadius: '50%',
              border: '2px solid white',
            }}
          />
          <Text size="sm">{t('common:last_exit', { defaultValue: 'Last Exit' })}</Text>
        </Group>
        
        <Group gap="xs">
          <Box
            style={{
              width: '20px',
              height: '8px',
              backgroundColor: '#22c55e',
              borderRadius: '4px',
              border: '2px solid #22c55e',
            }}
          />
          <Text size="sm">{t('common:currently_active', { defaultValue: 'Currently Active' })}</Text>
        </Group>
      </Group>
    </Card>
  );
};

export default PartyEntryExitTimeline;
