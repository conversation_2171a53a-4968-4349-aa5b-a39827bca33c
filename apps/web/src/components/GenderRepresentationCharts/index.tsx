import React from "react";
import {
  Card,
  Title,
  Text,
  Group,
  Badge,
  Stack,
  SimpleGrid,
  Progress,
} from "@mantine/core";
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { useTranslation } from "react-i18next";
import { IconMars, IconVenus, IconGenderBigender } from "@tabler/icons-react";

interface GenderData {
  governmentId: number;
  governmentName: string;
  startedAt: Date;
  male: number;
  female: number;
  other: number;
  total: number;
  femalePercentage: number;
}

interface GenderRepresentationChartsProps {
  data: GenderData[];
}

const GENDER_COLORS = {
  male: "#3b82f6",
  female: "#ec4899",
  other: "#8b5cf6",
};

const CustomTooltip = ({ active, payload, label }: any) => {
  const { t } = useTranslation();

  if (active && payload && payload.length) {
    return (
      <Card shadow="md" padding="sm" radius="md" withBorder>
        <Text fw={500} size="sm" mb="xs">
          {label}
        </Text>
        {payload.map((entry: any, index: number) => (
          <Text key={index} size="sm" c={entry.color}>
            {entry.name}: {entry.value}
            {entry.dataKey === "femalePercentage" && "%"}
          </Text>
        ))}
      </Card>
    );
  }

  return null;
};

const PieTooltip = ({ active, payload }: any) => {
  const { t } = useTranslation();

  if (active && payload && payload.length) {
    const data = payload[0];
    return (
      <Card shadow="md" padding="sm" radius="md" withBorder>
        <Text fw={500} size="sm">
          {data.name}: {data.value} (
          {((data.value / data.payload.total) * 100).toFixed(1)}%)
        </Text>
      </Card>
    );
  }

  return null;
};

const GenderIcon = ({ gender }: { gender: "male" | "female" | "other" }) => {
  switch (gender) {
    case "male":
      return <IconMars size={16} color={GENDER_COLORS.male} />;
    case "female":
      return <IconVenus size={16} color={GENDER_COLORS.female} />;
    case "other":
      return <IconGenderBigender size={16} color={GENDER_COLORS.other} />;
  }
};

const GenderRepresentationCharts: React.FC<GenderRepresentationChartsProps> = ({
  data,
}) => {
  const { t } = useTranslation();

  const sortedData = React.useMemo(() => {
    return [...data].sort(
      (a, b) =>
        new Date(a.startedAt).getTime() - new Date(b.startedAt).getTime()
    );
  }, [data]);

  const overallStats = React.useMemo(() => {
    const totals = data.reduce(
      (acc, gov) => ({
        male: acc.male + gov.male,
        female: acc.female + gov.female,
        other: acc.other + gov.other,
        total: acc.total + gov.total,
      }),
      { male: 0, female: 0, other: 0, total: 0 }
    );

    return {
      ...totals,
      femalePercentage:
        totals.total > 0 ? (totals.female / totals.total) * 100 : 0,
    };
  }, [data]);

  const trendData = sortedData.map((gov) => ({
    government:
      gov.governmentName.substring(0, 20) +
      (gov.governmentName.length > 20 ? "..." : ""),
    year: new Date(gov.startedAt).getFullYear(),
    femalePercentage: gov.femalePercentage,
    female: gov.female,
    male: gov.male,
    total: gov.total,
  }));

  const pieData = [
    {
      name: t("common:male", { defaultValue: "Male" }),
      value: overallStats.male,
      total: overallStats.total,
    },
    {
      name: t("common:female", { defaultValue: "Female" }),
      value: overallStats.female,
      total: overallStats.total,
    },
    {
      name: t("common:other", { defaultValue: "Other" }),
      value: overallStats.other,
      total: overallStats.total,
    },
  ].filter((item) => item.value > 0);

  const currentGov = data.find(
    (gov) => !gov.startedAt || new Date(gov.startedAt) > new Date()
  );
  const bestRepresentation = data.reduce((best, current) =>
    current.femalePercentage > best.femalePercentage ? current : best
  );

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Title order={3} mb="md">
        {t("common:gender_representation", {
          defaultValue: "Gender Representation in Cabinet",
        })}
      </Title>

      <SimpleGrid cols={{ base: 1, sm: 1, md: 2 }} spacing="xl" mb="xl">
        {/* Overall Gender Distribution */}
        <Stack>
          <Group justify="space-between">
            <Text fw={500} size="lg">
              {t("common:overall_distribution", {
                defaultValue: "Overall Distribution",
              })}
            </Text>
            <Badge variant="light" color="blue">
              {overallStats.total}{" "}
              {t("common:total_positions", { defaultValue: "total positions" })}
            </Badge>
          </Group>

          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={Object.values(GENDER_COLORS)[index]}
                  />
                ))}
              </Pie>
              <Tooltip content={<PieTooltip />} />
            </PieChart>
          </ResponsiveContainer>

          <Stack gap="xs">
            {pieData.map((item, index) => (
              <Group key={item.name} justify="space-between">
                <Group gap="xs">
                  <GenderIcon
                    gender={Object.keys(GENDER_COLORS)[index] as any}
                  />
                  <Text size="sm">{item.name}</Text>
                </Group>
                <Group gap="xs">
                  <Text size="sm" fw={500}>
                    {item.value} ({((item.value / item.total) * 100).toFixed(1)}
                    %)
                  </Text>
                </Group>
              </Group>
            ))}
          </Stack>
        </Stack>

        {/* Key Statistics */}
        <Stack>
          <Text fw={500} size="lg">
            {t("common:key_statistics", { defaultValue: "Key Statistics" })}
          </Text>

          <Stack gap="md">
            <div>
              <Group justify="space-between" mb="xs">
                <Text size="sm">
                  {t("common:current_female_representation", {
                    defaultValue: "Current Female Representation",
                  })}
                </Text>
                <Text size="sm" fw={500}>
                  {currentGov ? currentGov.femalePercentage.toFixed(1) : "0"}%
                </Text>
              </Group>
              <Progress
                value={currentGov ? currentGov.femalePercentage : 0}
                color="pink"
                size="lg"
              />
            </div>

            <div>
              <Group justify="space-between" mb="xs">
                <Text size="sm">
                  {t("common:best_representation", {
                    defaultValue: "Best Representation",
                  })}
                </Text>
                <Text size="sm" fw={500}>
                  {bestRepresentation.femalePercentage.toFixed(1)}%
                </Text>
              </Group>
              <Progress
                value={bestRepresentation.femalePercentage}
                color="green"
                size="lg"
              />
              <Text size="xs" c="dimmed" mt="xs">
                {bestRepresentation.governmentName}
              </Text>
            </div>

            <div>
              <Group justify="space-between" mb="xs">
                <Text size="sm">
                  {t("common:overall_average", {
                    defaultValue: "Overall Average",
                  })}
                </Text>
                <Text size="sm" fw={500}>
                  {overallStats.femalePercentage.toFixed(1)}%
                </Text>
              </Group>
              <Progress
                value={overallStats.femalePercentage}
                color="blue"
                size="lg"
              />
            </div>
          </Stack>
        </Stack>
      </SimpleGrid>

      {/* Trend Over Time */}
      <Stack>
        <Group justify="space-between">
          <Text fw={500} size="lg">
            {t("common:female_representation_trend", {
              defaultValue: "Female Representation Trend",
            })}
          </Text>
          <Badge variant="light" color="pink">
            {t("common:over_time", { defaultValue: "Over Time" })}
          </Badge>
        </Group>

        <ResponsiveContainer width="100%" height={300}>
          <LineChart
            data={trendData}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
          >
            <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
            <XAxis
              dataKey="government"
              angle={-45}
              textAnchor="end"
              height={80}
              fontSize={11}
            />
            <YAxis
              domain={[0, 100]}
              label={{
                value: t("common:female_percentage", {
                  defaultValue: "Female %",
                }),
                angle: -90,
                position: "insideLeft",
              }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Line
              type="monotone"
              dataKey="femalePercentage"
              stroke={GENDER_COLORS.female}
              strokeWidth={3}
              dot={{ r: 6, fill: GENDER_COLORS.female }}
              name={t("common:female_percentage", { defaultValue: "Female %" })}
            />
          </LineChart>
        </ResponsiveContainer>
      </Stack>
    </Card>
  );
};

export default GenderRepresentationCharts;
