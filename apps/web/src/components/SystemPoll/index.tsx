"use client";

import { useState, useEffect } from "react";
import {
  Card,
  Text,
  Title,
  Button,
  Radio,
  Checkbox,
  Badge,
  Group,
  Stack,
  Box,
  Alert,
  useMantineColorScheme,
  useMantineTheme,
} from "@mantine/core";
import { IconCheck, IconAlertCircle, IconUsers } from "@tabler/icons-react";
import { useTranslation } from "next-i18next";
import { ISystemPoll, IPollOption } from "@/interfaces/ISystemPoll";
import { ApiService } from "../../../api";
import { notifications } from "@mantine/notifications";
import { useProfile } from "@/store";
import { useLeaderHover } from "@/contexts/LeaderHoverContext";

interface SystemPollProps {
  poll: ISystemPoll;
  onVoteSubmitted?: (poll: ISystemPoll) => void;
  showResults?: boolean;
}

export default function SystemPoll({
  poll,
  onVoteSubmitted,
  showResults = false,
}: SystemPollProps) {
  const { t, i18n } = useTranslation();
  const profile = useProfile();
  const { openLoginModal } = useLeaderHover();
  const [selectedOptions, setSelectedOptions] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasUserVoted, setHasUserVoted] = useState(
    poll.hasUserResponded || false
  );
  const [currentPoll, setCurrentPoll] = useState<ISystemPoll>(poll);

  const isLoggedIn = !!profile.profile?.firstName;
  const isExpired = poll.deadline
    ? new Date() > new Date(poll.deadline)
    : false;
  const canChangeVote = !isExpired && !poll.deadline; // Can change vote if no deadline or not expired
  const shouldShowResults = showResults || hasUserVoted; // Always show results after voting
  const shouldShowVoteButton = !hasUserVoted || canChangeVote; // Show vote button if not voted or can change vote
  const isSingleChoice = poll.type === "RADIO";

  // Initialize user's previous votes if they exist
  useEffect(() => {
    if (poll.userResponse && poll.userResponse.length > 0) {
      const userOptionIds = poll.userResponse.map((option) => option.id);
      setSelectedOptions(userOptionIds);
    }
  }, [poll.userResponse]);

  // Update poll when it changes
  useEffect(() => {
    setCurrentPoll(poll);
  }, [poll]);

  const handleSingleSelect = (optionId: string) => {
    if (!shouldShowVoteButton) return; // Only prevent if vote button is not shown
    setSelectedOptions([parseInt(optionId)]);
  };

  const handleMultipleSelect = (optionId: number, checked: boolean) => {
    if (!shouldShowVoteButton) return; // Only prevent if vote button is not shown

    setSelectedOptions((prev) => {
      if (checked) {
        return [...prev, optionId];
      } else {
        return prev.filter((id) => id !== optionId);
      }
    });
  };

  const handleSubmit = async () => {
    if (!isLoggedIn) {
      openLoginModal();
      notifications.show({
        message: t("common:please_login_to_vote"),
        color: "red",
      });
      return;
    }

    if (selectedOptions.length === 0 || !shouldShowVoteButton) return;

    setIsSubmitting(true);

    try {
      await ApiService.voteOnPoll(poll.id, selectedOptions);

      setHasUserVoted(true);

      // Update the poll data with new vote counts
      // If user is changing vote, we need to handle the vote count changes differently
      const isChangingVote = poll.hasUserResponded && canChangeVote;
      const previousUserOptions = poll.userResponse?.map((opt) => opt.id) || [];

      const updatedOptions = currentPoll.options.map((option) => {
        let newVotes = option.votes;

        if (isChangingVote) {
          // Remove previous vote if this option was previously selected
          if (previousUserOptions.includes(option.id)) {
            newVotes = Math.max(0, newVotes - 1);
          }
        }

        // Add new vote if this option is currently selected
        if (selectedOptions.includes(option.id)) {
          newVotes = newVotes + 1;
        }

        return {
          ...option,
          votes: newVotes,
        };
      });

      // Total responses only increase if this is a new vote, not a changed vote
      const newTotalResponses = isChangingVote
        ? currentPoll.totalResponses
        : currentPoll.totalResponses + 1;

      // Recalculate percentages
      const updatedOptionsWithPercentages = updatedOptions.map((option) => ({
        ...option,
        percentage:
          newTotalResponses > 0
            ? Math.round((option.votes / newTotalResponses) * 100)
            : 0,
      }));

      const updatedPoll = {
        ...currentPoll,
        options: updatedOptionsWithPercentages,
        totalResponses: newTotalResponses,
        hasUserResponded: true,
        userResponse: updatedOptionsWithPercentages.filter((opt) =>
          selectedOptions.includes(opt.id)
        ),
      };

      setCurrentPoll(updatedPoll);

      notifications.show({
        message: isChangingVote
          ? t("common:vote_changed_successfully")
          : t("common:vote_submitted_successfully"),
        color: "green",
        icon: <IconCheck size={16} />,
      });

      onVoteSubmitted?.(updatedPoll);
    } catch (error) {
      console.error("Error submitting vote:", error);
      notifications.show({
        message: t("common:error_submitting_vote"),
        color: "red",
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getPercentage = (votes: number) => {
    if (currentPoll.totalResponses === 0) return 0;
    return Math.round((votes / currentPoll.totalResponses) * 100);
  };

  const getMaxVotes = () =>
    Math.max(...currentPoll.options.map((option) => option.votes));

  const getDisplayTitle = () => {
    return i18n.language === "ne" && poll.titleLocal
      ? poll.titleLocal
      : poll.title;
  };

  const getDisplayQuestion = () => {
    return i18n.language === "ne" && poll.questionLocal
      ? poll.questionLocal
      : poll.question;
  };

  const getDisplayDescription = () => {
    if (!poll.description) return null;
    return i18n.language === "ne" && poll.descriptionLocal
      ? poll.descriptionLocal
      : poll.description;
  };

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder mb="md">
      <Stack gap="md">
        {/* Poll Header */}
        <Group justify="space-between" align="flex-start">
          <Box style={{ flex: 1 }}>
            <Title order={4} mb="xs">
              {getDisplayTitle()}
            </Title>
            <Text size="sm" c="dimmed" mb="sm">
              {getDisplayQuestion()}
            </Text>
            {getDisplayDescription() && (
              <Text size="xs" c="dimmed" mb="sm">
                {getDisplayDescription()}
              </Text>
            )}
          </Box>
          <Group gap="xs" align="center">
            <Badge
              color={hasUserVoted ? "green" : "blue"}
              variant="light"
              leftSection={<IconUsers size={12} />}
            >
              {currentPoll.totalResponses} {t("common:votes")}
            </Badge>
          </Group>
        </Group>

        {/* Poll Options */}
        <Stack gap="sm">
          {isSingleChoice ? (
            <Radio.Group
              value={selectedOptions[0]?.toString() || ""}
              onChange={handleSingleSelect}
            >
              <Stack gap="xs">
                {currentPoll.options.map((option) => (
                  <PollOptionDisplay
                    key={option.id}
                    option={option}
                    isSelected={selectedOptions.includes(option.id)}
                    showResults={shouldShowResults}
                    percentage={getPercentage(option.votes)}
                    isHighest={
                      option.votes === getMaxVotes() &&
                      currentPoll.totalResponses > 0
                    }
                    onSelect={() => handleSingleSelect(option.id.toString())}
                    mode="single"
                    disabled={!shouldShowVoteButton}
                  />
                ))}
              </Stack>
            </Radio.Group>
          ) : (
            <Stack gap="xs">
              {currentPoll.options.map((option) => (
                <PollOptionDisplay
                  key={option.id}
                  option={option}
                  isSelected={selectedOptions.includes(option.id)}
                  showResults={shouldShowResults}
                  percentage={getPercentage(option.votes)}
                  isHighest={
                    option.votes === getMaxVotes() &&
                    currentPoll.totalResponses > 0
                  }
                  onSelect={(checked) =>
                    handleMultipleSelect(option.id, checked || false)
                  }
                  mode="multiple"
                  disabled={!shouldShowVoteButton}
                />
              ))}
            </Stack>
          )}
        </Stack>

        {/* Vote Button */}
        {shouldShowVoteButton && (
          <Group justify="center">
            <Button
              onClick={handleSubmit}
              disabled={selectedOptions.length === 0 || isSubmitting}
              loading={isSubmitting}
              color="blue"
              size="sm"
            >
              {isSubmitting
                ? t("common:submitting")
                : hasUserVoted && canChangeVote
                ? t("common:change_vote")
                : t("common:submit_vote")}
            </Button>
          </Group>
        )}

        {/* User Vote Status */}
        {hasUserVoted && !canChangeVote && (
          <Alert icon={<IconCheck size={16} />} color="green" variant="light">
            {isExpired
              ? t("common:poll_expired_voted")
              : t("common:you_have_voted")}
          </Alert>
        )}

        {/* Vote Change Available */}
        {hasUserVoted && canChangeVote && (
          <Alert icon={<IconCheck size={16} />} color="blue" variant="light">
            {t("common:you_can_change_vote")}
          </Alert>
        )}
      </Stack>
    </Card>
  );
}

// Helper component for poll option display
interface PollOptionDisplayProps {
  option: IPollOption;
  isSelected: boolean;
  showResults: boolean;
  percentage: number;
  isHighest: boolean;
  onSelect: (checked?: boolean) => void;
  mode: "single" | "multiple";
  disabled?: boolean;
}

function PollOptionDisplay({
  option,
  isSelected,
  showResults,
  percentage,
  isHighest,
  onSelect,
  mode,
  disabled = false,
}: PollOptionDisplayProps) {
  const { i18n } = useTranslation();
  const { colorScheme } = useMantineColorScheme();
  const theme = useMantineTheme();

  const getDisplayText = () => {
    return i18n.language === "ne" && option.textLocal
      ? option.textLocal
      : option.text;
  };

  // Dark mode friendly colors
  const isDark = colorScheme === "dark";

  const getBorderColor = () => {
    if (isSelected && !showResults) {
      return isDark ? theme.colors.blue[4] : theme.colors.blue[3];
    }
    return isDark ? theme.colors.dark[4] : theme.colors.gray[3];
  };

  const getBackgroundColor = () => {
    if (!showResults) return "transparent";

    return isDark
      ? isHighest
        ? theme.colors.dark[5]
        : theme.colors.dark[6]
      : isHighest
      ? theme.colors.gray[1]
      : theme.colors.gray[0];
  };

  const getProgressBarColor = () => {
    if (!showResults) return "transparent";

    return isDark
      ? isHighest
        ? theme.colors.blue[8]
        : theme.colors.dark[5]
      : isHighest
      ? theme.colors.blue[1]
      : theme.colors.gray[1];
  };

  return (
    <Box
      p="sm"
      style={{
        borderRadius: theme.radius.sm,
        cursor: showResults ? "default" : "pointer",
        backgroundColor: getBackgroundColor(),
        border: `1px solid ${getBorderColor()}`,
        position: "relative",
        overflow: "hidden",
        transition: "all 0.2s ease",
      }}
      onClick={() =>
        !showResults && onSelect(mode === "multiple" ? !isSelected : undefined)
      }
    >
      {/* Progress bar background for results */}
      {showResults && (
        <Box
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            height: "100%",
            width: `${percentage}%`,
            backgroundColor: getProgressBarColor(),
            transition: "width 0.3s ease",
            zIndex: 0,
          }}
        />
      )}

      <Group
        justify="space-between"
        style={{ position: "relative", zIndex: 1 }}
      >
        <Group gap="sm">
          {mode === "single" ? (
            <Radio value={option.id.toString()} disabled={disabled} size="sm" />
          ) : (
            <Checkbox
              checked={isSelected}
              disabled={disabled}
              size="sm"
              onChange={(event) => onSelect(event.currentTarget.checked)}
            />
          )}
          <Text size="sm" fw={isHighest && showResults ? 600 : 400}>
            {getDisplayText()}
          </Text>
        </Group>

        {showResults && (
          <Group gap="xs">
            <Text size="sm" fw={500}>
              {option.votes}
            </Text>
            <Text size="sm" c="dimmed">
              ({percentage}%)
            </Text>
          </Group>
        )}
      </Group>
    </Box>
  );
}
