import React from 'react';
import { render, screen } from '@testing-library/react';
import { MantineProvider } from '@mantine/core';
import { QueryClient, QueryClientProvider } from 'react-query';
import PartyCabinetStrengthChart from '../PartyCabinetStrengthChart';
import MinisterBehaviorAnalysis from '../MinisterBehaviorAnalysis';
import CabinetReshuffleHeatmap from '../CabinetReshuffleHeatmap';
import GenderRepresentationCharts from '../GenderRepresentationCharts';
import PartyEntryExitTimeline from '../PartyEntryExitTimeline';

// Mock react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, options?: any) => options?.defaultValue || key,
    i18n: {
      changeLanguage: () => new Promise(() => {}),
    },
  }),
}));

// <PERSON>ck recharts
jest.mock('recharts', () => ({
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>,
  LineChart: ({ children }: any) => <div data-testid="line-chart">{children}</div>,
  BarChart: ({ children }: any) => <div data-testid="bar-chart">{children}</div>,
  PieChart: ({ children }: any) => <div data-testid="pie-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  Bar: () => <div data-testid="bar" />,
  Pie: () => <div data-testid="pie" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
  Cell: () => <div data-testid="cell" />,
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <MantineProvider>
        {children}
      </MantineProvider>
    </QueryClientProvider>
  );
};

describe('Government Analytics Components', () => {
  const mockPartyCabinetData = [
    {
      governmentId: 1,
      governmentName: 'Test Government 1',
      startedAt: new Date('2020-01-01'),
      endAt: new Date('2021-01-01'),
      partyId: 1,
      partyName: 'Test Party A',
      partyCode: 'TPA',
      partyLogo: '/test-logo.png',
      partyColorCode: '#ff0000',
      ministerCount: 5,
    },
    {
      governmentId: 2,
      governmentName: 'Test Government 2',
      startedAt: new Date('2021-01-01'),
      endAt: null,
      partyId: 2,
      partyName: 'Test Party B',
      partyCode: 'TPB',
      partyLogo: '/test-logo-2.png',
      partyColorCode: '#00ff00',
      ministerCount: 8,
    },
  ];

  const mockMinisterBehaviorData = {
    recurringMinisters: [
      {
        leaderId: 1,
        leaderName: 'John Doe',
        leaderLocalName: 'जोन डो',
        leaderImg: '/john-doe.jpg',
        appearances: 3,
        governments: 'Gov1, Gov2, Gov3',
        totalTenure: 1095, // 3 years in days
      },
    ],
    partySwitchers: [
      {
        leaderId: 2,
        leaderName: 'Jane Smith',
        leaderLocalName: 'जेन स्मिथ',
        leaderImg: '/jane-smith.jpg',
        partyChanges: 2,
        parties: 'Party A, Party B',
      },
    ],
  };

  const mockReshuffleData = [
    {
      departmentId: 1,
      departmentName: 'Ministry of Finance',
      reshuffleCount: 5,
      avgTenure: 365,
    },
    {
      departmentId: 2,
      departmentName: 'Ministry of Education',
      reshuffleCount: 3,
      avgTenure: 500,
    },
  ];

  const mockGenderData = [
    {
      governmentId: 1,
      governmentName: 'Test Government 1',
      startedAt: new Date('2020-01-01'),
      male: 15,
      female: 5,
      other: 0,
      total: 20,
      femalePercentage: 25,
    },
  ];

  const mockPartyTimelineData = [
    {
      partyId: 1,
      partyName: 'Test Party A',
      partyCode: 'TPA',
      partyLogo: '/test-logo.png',
      partyColorCode: '#ff0000',
      firstEntry: new Date('2015-01-01'),
      lastExit: new Date('2020-01-01'),
      totalGovernments: 2,
      totalMinisters: 10,
    },
  ];

  describe('PartyCabinetStrengthChart', () => {
    it('renders without crashing', () => {
      render(
        <TestWrapper>
          <PartyCabinetStrengthChart data={mockPartyCabinetData} />
        </TestWrapper>
      );
      
      expect(screen.getByText(/Party Cabinet Strength Over Time/i)).toBeInTheDocument();
      expect(screen.getByTestId('responsive-container')).toBeInTheDocument();
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    });

    it('displays party count badge', () => {
      render(
        <TestWrapper>
          <PartyCabinetStrengthChart data={mockPartyCabinetData} />
        </TestWrapper>
      );
      
      expect(screen.getByText(/2.*Parties/i)).toBeInTheDocument();
    });
  });

  describe('MinisterBehaviorAnalysis', () => {
    it('renders without crashing', () => {
      render(
        <TestWrapper>
          <MinisterBehaviorAnalysis data={mockMinisterBehaviorData} />
        </TestWrapper>
      );
      
      expect(screen.getByText(/Minister Behavior Analysis/i)).toBeInTheDocument();
      expect(screen.getByText(/Recurring Ministers/i)).toBeInTheDocument();
      expect(screen.getByText(/Party Switchers/i)).toBeInTheDocument();
    });

    it('displays minister data correctly', () => {
      render(
        <TestWrapper>
          <MinisterBehaviorAnalysis data={mockMinisterBehaviorData} />
        </TestWrapper>
      );
      
      expect(screen.getByText('जोन डो')).toBeInTheDocument();
      expect(screen.getByText('जेन स्मिथ')).toBeInTheDocument();
    });
  });

  describe('CabinetReshuffleHeatmap', () => {
    it('renders without crashing', () => {
      render(
        <TestWrapper>
          <CabinetReshuffleHeatmap data={mockReshuffleData} />
        </TestWrapper>
      );
      
      expect(screen.getByText(/Cabinet Reshuffle Analysis/i)).toBeInTheDocument();
      expect(screen.getByTestId('responsive-container')).toBeInTheDocument();
      expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
    });

    it('displays ministry data', () => {
      render(
        <TestWrapper>
          <CabinetReshuffleHeatmap data={mockReshuffleData} />
        </TestWrapper>
      );
      
      expect(screen.getByText('Ministry of Finance')).toBeInTheDocument();
      expect(screen.getByText('Ministry of Education')).toBeInTheDocument();
    });
  });

  describe('GenderRepresentationCharts', () => {
    it('renders without crashing', () => {
      render(
        <TestWrapper>
          <GenderRepresentationCharts data={mockGenderData} />
        </TestWrapper>
      );
      
      expect(screen.getByText(/Gender Representation in Cabinet/i)).toBeInTheDocument();
      expect(screen.getByTestId('responsive-container')).toBeInTheDocument();
    });

    it('displays gender statistics', () => {
      render(
        <TestWrapper>
          <GenderRepresentationCharts data={mockGenderData} />
        </TestWrapper>
      );
      
      expect(screen.getByText(/25.0%/)).toBeInTheDocument(); // Female percentage
    });
  });

  describe('PartyEntryExitTimeline', () => {
    it('renders without crashing', () => {
      render(
        <TestWrapper>
          <PartyEntryExitTimeline data={mockPartyTimelineData} />
        </TestWrapper>
      );
      
      expect(screen.getByText(/Party Government Timeline/i)).toBeInTheDocument();
      expect(screen.getByText('Test Party A')).toBeInTheDocument();
    });

    it('displays timeline statistics', () => {
      render(
        <TestWrapper>
          <PartyEntryExitTimeline data={mockPartyTimelineData} />
        </TestWrapper>
      );
      
      expect(screen.getByText(/Total Parties/i)).toBeInTheDocument();
      expect(screen.getByText(/Currently Active/i)).toBeInTheDocument();
    });
  });
});
