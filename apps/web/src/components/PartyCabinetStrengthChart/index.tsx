import React, { useMemo, memo } from "react";
import { Card, Title, Text, Group, Avatar, Badge } from "@mantine/core";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LabelList,
} from "recharts";
import { useTranslation } from "react-i18next";
import { getImageUrlWithFallback } from "@/utils";
import { PartyCabinetStrengthChartProps } from "@/interfaces/IGovernmentAnalytics";

const CustomDot = memo((props: any) => {
  const { cx, cy, payload } = props;
  if (!payload || !payload.partyLogo) return null;

  return (
    <foreignObject x={cx - 10} y={cy - 10} width={20} height={20}>
      <img
        src={getImageUrlWithFallback(payload.partyLogo, payload.partyCode)}
        alt={payload.partyName}
        style={{
          width: "20px",
          height: "20px",
          borderRadius: "50%",
          border: "2px solid white",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        }}
      />
    </foreignObject>
  );
});

const CustomTooltip = memo(({ active, payload, label }: any) => {
  const { t } = useTranslation();

  if (active && payload && payload.length) {
    return (
      <Card shadow="md" padding="sm" radius="md" withBorder>
        <Text fw={500} size="sm" mb="xs">
          {label}
        </Text>
        {payload.map((entry: any, index: number) => (
          <Group key={index} gap="xs" mb="xs">
            <Avatar
              src={getImageUrlWithFallback(
                entry.payload[entry.dataKey + "_data"].partyLogo,
                entry.payload.partyCode
              )}
              size="xs"
              radius="xl"
            />
            <Text size="sm" c={entry.color}>
              {entry.payload[entry.dataKey + "_data"].partyName}: {entry.value}{" "}
              {t("common:ministers", { defaultValue: "ministers" })}
            </Text>
          </Group>
        ))}
      </Card>
    );
  }

  return null;
});

const CustomLegend = memo(({ payload }: any) => {
  return (
    <Group justify="center" gap="md" mt="md">
      {payload.slice(0, 10).map((entry: any, index: number) => (
        <Group key={index} gap="xs">
          <Avatar
            src={getImageUrlWithFallback(
              entry.payload?.partyLogo,
              entry.payload?.partyCode
            )}
            size="xs"
            radius="xl"
          />
          <Text size="sm" c={entry.color}>
            {entry.value}
          </Text>
        </Group>
      ))}
    </Group>
  );
});

const PartyCabinetStrengthChart: React.FC<PartyCabinetStrengthChartProps> =
  memo(({ data }) => {
    const { t } = useTranslation();

    // Transform data for chart
    const chartData = React.useMemo(() => {
      // Group by government
      const governmentMap = new Map();

      data.forEach((item) => {
        const govKey = item.governmentId;
        if (!governmentMap.has(govKey)) {
          governmentMap.set(govKey, {
            governmentName: item.governmentName,
            startYear: new Date(item.startedAt).getFullYear(),
            endYear: item.endAt
              ? new Date(item.endAt).getFullYear()
              : "Present",
          });
        }

        governmentMap.get(govKey)[item.partyCode] = item.ministerCount;
        governmentMap.get(govKey)[`${item.partyCode}_data`] = {
          partyName: item.partyName,
          partyLogo: item.partyLogo,
          partyCode: item.partyCode,
          partyColorCode: item.partyColorCode,
        };
      });

      return Array.from(governmentMap.entries()).map(([govId, govData]) => ({
        government: govData.governmentName,
        period: `${govData.startYear}-${govData.endYear}`,
        ...govData,
      }));
    }, [data]);

    // Get unique parties for lines
    const parties = React.useMemo(() => {
      const partySet = new Set();
      data.forEach((item) => {
        partySet.add(
          JSON.stringify({
            code: item.partyCode,
            name: item.partyName,
            logo: item.partyLogo,
            color: item.partyColorCode || "#8884d8",
          })
        );
      });
      return Array.from(partySet).map((p) => JSON.parse(p as string));
    }, [data]);

    const renderDot = (logo?: string) => (props: any) => {
      const { cx, cy } = props;
      if (!cy) return null;
      return logo ? (
        <image
          href={
            logo === "NULL"
              ? "/logos/nepaltracks.png"
              : logo || "/logos/nepaltracks.png"
          }
          x={cx - 10}
          y={cy - 10}
          height={20}
          width={20}
        />
      ) : null;
    };

    return (
      <Card shadow="sm" padding="lg" radius="md" withBorder>
        <Group justify="space-between" mb="lg">
          <div>
            <Title order={3}>
              {t("common:party_cabinet_strength", {
                defaultValue: "Party Cabinet Strength Over Time",
              })}
            </Title>
            <Text c="dimmed" size="sm">
              {t("common:party_cabinet_strength_desc", {
                defaultValue:
                  "Number of cabinet positions held by each party across governments",
              })}
            </Text>
          </div>
          <Badge variant="light" color="blue">
            {parties.length} {t("common:parties", { defaultValue: "Parties" })}
          </Badge>
        </Group>

        <ResponsiveContainer width="100%" height={400}>
          <LineChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
            aria-label={t("common:party_cabinet_strength", {
              defaultValue: "Party Cabinet Strength Over Time",
            })}
            role="img"
          >
            <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
            <XAxis
              dataKey="period"
              angle={-45}
              textAnchor="end"
              height={80}
              fontSize={12}
            />
            <YAxis
              label={{
                value: t("common:number_of_ministers", {
                  defaultValue: "Number of Ministers",
                }),
                angle: -90,
                position: "insideLeft",
              }}
            />
            <Tooltip content={<CustomTooltip />} />
            {/* <Legend content={<CustomLegend />} /> */}

            {parties.map((party, index) => (
              <Line
                key={party.code}
                type="monotone"
                dot={renderDot(party.logo)}
                dataKey={party.code}
                stroke={party.color}
                strokeWidth={3}
                // dot={<CustomDot />}
                connectNulls={false}
                name={party.name}
              />
            ))}
          </LineChart>
        </ResponsiveContainer>
      </Card>
    );
  });

export default PartyCabinetStrengthChart;
