import { IGovernment } from "./IGovernment";

export interface PartyCabinetData {
  governmentId: number;
  governmentName: string;
  startedAt: Date;
  endAt: Date | null;
  partyId: number;
  partyName: string;
  partyCode: string;
  partyLogo: string | null;
  partyColorCode: string | null;
  ministerCount: number;
}

export interface RecurringMinister {
  leaderId: number;
  leaderName: string;
  leaderLocalName: string;
  leaderImg: string | null;
  appearances: number;
  governments: string;
  totalTenure: number;
}

export interface PartySwitcher {
  leaderId: number;
  leaderName: string;
  leaderLocalName: string;
  leaderImg: string | null;
  partyChanges: number;
  parties: string;
}

export interface MinisterBehaviorData {
  recurringMinisters: RecurringMinister[];
  partySwitchers: PartySwitcher[];
}

export interface ReshuffleData {
  departmentId: number;
  departmentName: string;
  reshuffleCount: number;
  avgTenure: number;
}

export interface GenderData {
  governmentId: number;
  governmentName: string;
  startedAt: Date;
  male: number;
  female: number;
  other: number;
  total: number;
  femalePercentage: number;
}

export interface PartyTimelineData {
  partyId: number;
  partyName: string;
  partyCode: string;
  partyLogo: string | null;
  partyColorCode: string | null;
  firstEntry: Date;
  lastExit: Date | null;
  totalGovernments: number;
  totalMinisters: number;
}

export interface GovernmentWithSummary extends IGovernment {
  cabinetSummary: {
    totalMinisters: number;
    parties: string[];
    reshuffles: number;
  };
}

export interface GovernmentIndexAnalytics {
  governments: GovernmentWithSummary[];
  partyCabinetStrength: PartyCabinetData[];
  ministerBehavior: MinisterBehaviorData;
  cabinetReshuffles: ReshuffleData[];
  genderRepresentation: GenderData[];
  partyTimeline: PartyTimelineData[];
}

// Chart-specific interfaces
export interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

export interface ChartLegendProps {
  payload?: any[];
}

export interface ResponsiveChartProps {
  width?: string | number;
  height?: string | number;
}

// Component props interfaces
export interface PartyCabinetStrengthChartProps {
  data: PartyCabinetData[];
}

export interface MinisterBehaviorAnalysisProps {
  data: MinisterBehaviorData;
}

export interface CabinetReshuffleHeatmapProps {
  data: ReshuffleData[];
}

export interface GenderRepresentationChartsProps {
  data: GenderData[];
}

export interface PartyEntryExitTimelineProps {
  data: PartyTimelineData[];
}

export interface GovernmentCardProps {
  government: GovernmentWithSummary;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Error handling
export interface ChartError {
  message: string;
  code?: string;
  details?: any;
}

// Loading states
export interface LoadingState {
  isLoading: boolean;
  error?: ChartError | null;
  data?: any;
}

// Chart configuration
export interface ChartConfig {
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  colors?: string[];
  theme?: 'light' | 'dark';
  animations?: boolean;
}

// Accessibility
export interface ChartAccessibility {
  ariaLabel?: string;
  ariaDescription?: string;
  role?: string;
  tabIndex?: number;
}

// Export all interfaces
export type {
  IGovernment,
  PartyCabinetData,
  RecurringMinister,
  PartySwitcher,
  MinisterBehaviorData,
  ReshuffleData,
  GenderData,
  PartyTimelineData,
  GovernmentWithSummary,
  GovernmentIndexAnalytics,
  ChartTooltipProps,
  ChartLegendProps,
  ResponsiveChartProps,
  PartyCabinetStrengthChartProps,
  MinisterBehaviorAnalysisProps,
  CabinetReshuffleHeatmapProps,
  GenderRepresentationChartsProps,
  PartyEntryExitTimelineProps,
  GovernmentCardProps,
  ApiResponse,
  PaginatedResponse,
  ChartError,
  LoadingState,
  ChartConfig,
  ChartAccessibility,
};
