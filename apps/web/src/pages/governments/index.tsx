import {
  Box,
  Card,
  Group,
  Stack,
  Text,
  Title,
  SimpleGrid,
  Badge,
  Avatar,
  Button,
  Skeleton,
  Container,
  Ta<PERSON>,
  Alert,
} from "@mantine/core";
import {
  IconCalendar,
  IconUsers,
  IconBuilding,
  IconTrendingUp,
  IconInfoCircle,
} from "@tabler/icons-react";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import { loadTranslation } from "@/i18n";
import { GetServerSideProps } from "next";
import Head from "next/head";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { formatDate, getImageUrlWithFallback } from "@/utils";
import { SectionTitle } from "@/components/SectionTitle";
import PartyCabinetStrengthChart from "@/components/PartyCabinetStrengthChart";
import MinisterBehaviorAnalysis from "@/components/MinisterBehaviorAnalysis";
import CabinetReshuffleHeatmap from "@/components/CabinetReshuffleHeatmap";
import GenderRepresentationCharts from "@/components/GenderRepresentationCharts";
import PartyEntryExitTimeline from "@/components/PartyEntryExitTimeline";
import AppBreadCrumb from "@/components/AppBreadCrumb";
import {
  GovernmentIndexAnalytics,
  GovernmentWithSummary,
  GovernmentCardProps,
} from "@/interfaces/IGovernmentAnalytics";

export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const translation = await loadTranslation(context.locale!, "common");

  return {
    props: {
      ...translation,
    },
  };
};

const GovernmentCard = ({ government }: GovernmentCardProps) => {
  const { t } = useTranslation();

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder h="100%">
      <Stack justify="space-between" h="100%">
        <div>
          <Group justify="space-between" mb="xs">
            <Group>
              <Avatar
                src={getImageUrlWithFallback(
                  government.head?.img,
                  government.head?.ecCandidateID
                )}
                size="md"
                radius="xl"
              />
              <Box>
                <Text fw={500} size="sm" lineClamp={1}>
                  {government.head?.localName || government.head?.name}
                </Text>
                <Text size="xs" c="dimmed">
                  {t("common:prime_minister", {
                    defaultValue: "Prime Minister",
                  })}
                </Text>
              </Box>
            </Group>
            <Badge
              color={government.endAt ? "gray" : "green"}
              variant="light"
              size="sm"
            >
              {government.government_type}
            </Badge>
          </Group>

          <Group justify="space-between" mb="md">
            <Group gap="xs">
              <IconCalendar size={16} />
              <Text size="xs" c="dimmed" lineClamp={1}>
                {formatDate(government.startedAt)} -{" "}
                {government.endAt
                  ? formatDate(government.endAt)
                  : t("common:present")}
              </Text>
            </Group>
            {!government.endAt && (
              <Badge color="green" size="xs">
                {t("common:current")}
              </Badge>
            )}
          </Group>

          <Stack gap="xs" mb="md">
            <Group gap="xs">
              <IconUsers size={16} />
              <Text size="sm">
                {government.cabinetSummary.totalMinisters}{" "}
                {t("common:ministers", { defaultValue: "Ministers" })}
              </Text>
            </Group>

            <Group gap="xs">
              <IconBuilding size={16} />
              <Text size="sm">
                {government.cabinetSummary.parties.length}{" "}
                {t("common:parties", { defaultValue: "Parties" })}
              </Text>
            </Group>

            {government.cabinetSummary.reshuffles > 0 && (
              <Group gap="xs">
                <IconTrendingUp size={16} />
                <Text size="sm">
                  {government.cabinetSummary.reshuffles}{" "}
                  {t("common:reshuffles", { defaultValue: "Reshuffles" })}
                </Text>
              </Group>
            )}
          </Stack>
        </div>

        <Button
          component={Link}
          href={`/governments/${government.id}/home`}
          variant="light"
          fullWidth
          size="sm"
        >
          {t("common:view_full_cabinet", { defaultValue: "View Full Cabinet" })}
        </Button>
      </Stack>
    </Card>
  );
};

const LoadingSkeleton = () => (
  <Stack gap="xl">
    <Skeleton height={400} radius="md" />
    <Skeleton height={300} radius="md" />
    <Skeleton height={350} radius="md" />
    <Skeleton height={400} radius="md" />
    <Skeleton height={300} radius="md" />
  </Stack>
);

const Government = () => {
  const { t } = useTranslation();

  const {
    data: analytics,
    isLoading,
    error,
  } = useQuery<GovernmentIndexAnalytics>(
    "government-index-analytics",
    async () => {
      const response = await ApiService.resource.getAll(
        "governments/index-analytics",
        {}
      );
      return response.data;
    },
    {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  if (error) {
    return (
      <Container size="xl">
        <Alert
          icon={<IconInfoCircle size={16} />}
          title={t("common:error", { defaultValue: "Error" })}
          color="red"
        >
          {t("common:failed_to_load_data", {
            defaultValue:
              "Failed to load government data. Please try again later.",
          })}
        </Alert>
      </Container>
    );
  }

  return (
    <>
      <Head>
        <title>
          {t("common:governments")} | NepalTracks – Nepal's Ultimate Political
          Tracker | Leaders, Elections & Parties
        </title>
        <meta
          name="description"
          content={t("common:government_index_meta_description", {
            defaultValue:
              "Comprehensive analysis of all governments in Nepal, including cabinet compositions, party trends, and political insights over time.",
          })}
        />
      </Head>

      <Container size="xl">
        <Stack gap="xl">
          <AppBreadCrumb
            links={[
              {
                label: "governments",
                href: "/governments",
              },
            ]}
          />

          <Box>
            <Title order={1} mb="md">
              {t("common:government_index", {
                defaultValue: "Government Index",
              })}
            </Title>
            <Text c="dimmed" size="lg">
              {t("common:government_index_description", {
                defaultValue:
                  "Comprehensive analysis of all governments, cabinet compositions, and political trends over time",
              })}
            </Text>
          </Box>

          <Tabs defaultValue="overview" variant="outline">
            <Tabs.List>
              <Tabs.Tab value="overview">
                {t("common:overview", { defaultValue: "Overview" })}
              </Tabs.Tab>
              <Tabs.Tab value="analytics">
                {t("common:analytics", { defaultValue: "Analytics" })}
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="overview" pt="xl">
              <Stack gap="xl">
                <SectionTitle
                  title={t("common:all_governments", {
                    defaultValue: "All Governments",
                  })}
                  description={t("common:chronological_list", {
                    defaultValue: "Chronological list of all governments",
                  })}
                />

                {isLoading ? (
                  <SimpleGrid cols={{ base: 1, sm: 2, lg: 3 }}>
                    {Array.from({ length: 6 }).map((_, index) => (
                      <Skeleton key={index} height={280} radius="md" />
                    ))}
                  </SimpleGrid>
                ) : analytics?.governments ? (
                  <SimpleGrid cols={{ base: 1, sm: 2, lg: 3 }}>
                    {analytics.governments.map((government) => (
                      <GovernmentCard
                        key={government.id}
                        government={government}
                      />
                    ))}
                  </SimpleGrid>
                ) : (
                  <Alert
                    icon={<IconInfoCircle size={16} />}
                    title={t("common:no_data", { defaultValue: "No Data" })}
                  >
                    {t("common:no_governments_found", {
                      defaultValue: "No governments found.",
                    })}
                  </Alert>
                )}
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="analytics" pt="xl">
              <Stack gap="xl">
                {isLoading ? (
                  <LoadingSkeleton />
                ) : analytics ? (
                  <>
                    <PartyCabinetStrengthChart
                      data={analytics.partyCabinetStrength}
                    />
                    <MinisterBehaviorAnalysis
                      data={analytics.ministerBehavior}
                    />
                    <CabinetReshuffleHeatmap
                      data={analytics.cabinetReshuffles}
                    />
                    <GenderRepresentationCharts
                      data={analytics.genderRepresentation}
                    />
                    <PartyEntryExitTimeline data={analytics.partyTimeline} />
                  </>
                ) : (
                  <Alert
                    icon={<IconInfoCircle size={16} />}
                    title={t("common:no_data", { defaultValue: "No Data" })}
                  >
                    {t("common:no_analytics_data", {
                      defaultValue: "No analytics data available.",
                    })}
                  </Alert>
                )}
              </Stack>
            </Tabs.Panel>
          </Tabs>
        </Stack>
      </Container>
    </>
  );
};

export default Government;
