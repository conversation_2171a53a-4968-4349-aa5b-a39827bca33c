const fs = require('fs');
const path = require('path');

// Read the reference files
const leadersData = JSON.parse(fs.readFileSync('./leaders (5).json', 'utf8'));
const partiesData = JSON.parse(fs.readFileSync('./parties (2).json', 'utf8'));
const districtsData = JSON.parse(fs.readFileSync('./districts.json', 'utf8'));

// Extract the actual data arrays from the reference files
const leaders = leadersData.find(item => item.type === 'table' && item.name === 'leaders')?.data || [];
const parties = partiesData;
const districts = districtsData.find(item => item.type === 'table' && item.name === 'districts')?.data || [];

// Create lookup maps
const leaderMap = {};
leaders.forEach(leader => {
    if (leader.name && leader.localName) {
        leaderMap[leader.name.toLowerCase()] = leader.localName;
    }
});

const partyMap = {};
parties.forEach(party => {
    if (party.name && party.localName) {
        // Handle various party name variations
        partyMap[party.name.toLowerCase()] = party.localName;
        
        // Add specific mappings for common variations
        if (party.name.includes('Communist Party of Nepal (UML)') || party.name.includes('Communist Party of Nepal (Unified Marxist–Leninist)')) {
            partyMap['communist party of nepal (unified marxist–leninist)'] = party.localName;
            partyMap['communist party of nepal (uml)'] = party.localName;
        }
        if (party.name.includes('Nepali Congress') || party.name.includes('The Nepali Congress')) {
            partyMap['the nepali congress'] = party.localName;
            partyMap['nepali congress'] = party.localName;
        }
    }
});

const districtMap = {};
districts.forEach(district => {
    if (district.name && district.localName) {
        districtMap[district.name.toLowerCase()] = district.localName;
        
        // Handle case variations
        if (district.name.toLowerCase() === 'ilaam') {
            districtMap['ilam'] = district.localName;
        }
        if (district.name.toLowerCase() === 'panchthar') {
            districtMap['panchthar'] = district.localName;
        }
    }
});

// Function to transliterate English names to Nepali (basic approximation)
function transliterateToNepali(englishName) {
    // This is a basic transliteration - in practice, you'd want a more sophisticated approach
    const transliterationMap = {
        'a': 'अ', 'aa': 'आ', 'i': 'इ', 'ii': 'ई', 'u': 'उ', 'uu': 'ऊ',
        'e': 'ए', 'ai': 'ऐ', 'o': 'ओ', 'au': 'औ',
        'ka': 'क', 'kha': 'ख', 'ga': 'ग', 'gha': 'घ', 'nga': 'ङ',
        'cha': 'च', 'chha': 'छ', 'ja': 'ज', 'jha': 'झ', 'nya': 'ञ',
        'ta': 'ट', 'tha': 'ठ', 'da': 'ड', 'dha': 'ढ', 'na': 'ण',
        'ta': 'त', 'tha': 'थ', 'da': 'द', 'dha': 'ध', 'na': 'न',
        'pa': 'प', 'pha': 'फ', 'ba': 'ब', 'bha': 'भ', 'ma': 'म',
        'ya': 'य', 'ra': 'र', 'la': 'ल', 'wa': 'व', 'sha': 'श',
        'shha': 'ष', 'sa': 'स', 'ha': 'ह'
    };
    
    // Simple name-based transliteration for common names
    const nameMap = {
        'kul prasad uprety': 'कुल प्रसाद उप्रेती',
        'mani lama': 'मणि लामा',
        'basanta kumar nemwang': 'बसन्त कुमार नेम्वाङ',
        'dipak pr. baskota': 'दिपक प्र. बास्कोटा',
        'damber sing sambahamphe': 'दम्बर सिंह साम्बाहाम्फे',
        'bhishma raj angdambe': 'भीष्म राज आङदाम्बे',
        'jhala nath khanal': 'झलनाथ खनाल',
        'chandra k. shrestha': 'चन्द्र के. श्रेष्ठ',
        'mani kumar limbu': 'मणि कुमार लिम्बु',
        'tek bahadur chokhal': 'टेक बहादुर चोखल',
        'krishna prasad sitaula': 'कृष्ण प्रसाद सिटौला',
        'bhoj raj ghimire': 'भोज राज घिमिरे',
        'devi prasad ojha': 'देवी प्रसाद ओझा',
        'chandra m. ghimire': 'चन्द्र म. घिमिरे',
        'radha krishna mainali': 'राधा कृष्ण मैनाली',
        'bidya bir singh kansakar': 'विद्या बीर सिंह कंसकार',
        'narayan singh rajbanshi': 'नारायण सिंह राजवंशी',
        'ram babu prasai': 'राम बाबु प्रसाई',
        'chandra prakash mainali': 'चन्द्र प्रकाश मैनाली',
        'bhupendra nepali': 'भुपेन्द्र नेपाली',
        'k.p. sharma oli': 'के.पी. शर्मा ओली'
    };
    
    const lowerName = englishName.toLowerCase();
    return nameMap[lowerName] || englishName; // Return original if no mapping found
}

// Read the election results file
const electionResults = JSON.parse(fs.readFileSync('./nepal_1991_election_results_COMPLETE.json', 'utf8'));

// Update the election results
electionResults.forEach(result => {
    // Update candidate name
    const candidateKey = result.CandidateName.toLowerCase();
    result.candidateNameNP = leaderMap[candidateKey] || transliterateToNepali(result.CandidateName);
    
    // Update district name
    const districtKey = result.DistrictName.toLowerCase();
    result.districtNameNP = districtMap[districtKey] || result.DistrictName;
    
    // Update party name (already seems to be updated in the file)
    const partyKey = result.PoliticalPartyName.toLowerCase();
    if (!result.politicalPartyNameNP || result.politicalPartyNameNP === '') {
        result.politicalPartyNameNP = partyMap[partyKey] || result.PoliticalPartyName;
    }
});

// Write the updated file
fs.writeFileSync('./nepal_1991_election_results_COMPLETE_updated.json', JSON.stringify(electionResults, null, 2));

console.log('Updated election results with Nepali names');
console.log(`Processed ${electionResults.length} records`);
