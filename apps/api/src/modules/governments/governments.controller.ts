import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UsePipes,
  ValidationPipe,
  Query,
  UseInterceptors,
} from '@nestjs/common';
import { GovernmentsService } from './governments.service';
import { CreateGovernmentDto } from './dto/create-government.dto';
import { UpdateGovernmentDto } from './dto/update-government.dto';
import { FindOneParams } from 'src/dtos/FindOneParams';
import { PaginationSortSearchDto } from '../leaders/dto/index.dto';
import { GOVERNMENT_TYPE } from '@prisma/client';
import { RequestOriginCacheInterceptor } from 'src/interceptors/RequestOriginCacheInterceptor';

@Controller('governments')
@UsePipes(ValidationPipe)
@UseInterceptors(RequestOriginCacheInterceptor)
export class GovernmentsController {
  constructor(private readonly governmentsService: GovernmentsService) {}

  @Post()
  create(@Body() createGovernmentDto: CreateGovernmentDto) {
    return this.governmentsService.create(createGovernmentDto);
  }

  @Get('/analytics')
  analytics(@Query() query: { partyId?: number }) {
    return this.governmentsService.analytics(query);
  }

  @Get('/index-analytics')
  getGovernmentIndexAnalytics() {
    return this.governmentsService.getGovernmentIndexAnalytics();
  }

  @Get('/level/:level')
  findAll(
    @Param('level') params: string,
    @Query() query: PaginationSortSearchDto,
  ) {
    return this.governmentsService.findAll(params, query);
  }

  @Get('/:id')
  findOne(@Param() params: FindOneParams & { level: GOVERNMENT_TYPE }) {
    return this.governmentsService.findOne(+params.id, params.level);
  }
  @Get('/:id/hot-cabinet-members')
  getPopularCabinetMembers(
    @Query() query: PaginationSortSearchDto,
    @Param('id') governmentId: string,
  ) {
    return this.governmentsService.getPopularCabinetMembers(
      query,
      +governmentId,
    );
  }

  @Get('/:level/:id/overview')
  async getPartiesOfGovernment(
    @Param() params: FindOneParams & { level: GOVERNMENT_TYPE },
  ) {
    const partiesAndMemmbers =
      await this.governmentsService.getOverviewOfGovernment(
        +params.id,
        params.level,
      );
    return {
      coaltion: partiesAndMemmbers.coaltion,
      ...partiesAndMemmbers,
    };
  }
  @Get('/:level/:id/ministries')
  getMinistriesOfGovernment(
    @Param() params: FindOneParams & { level: GOVERNMENT_TYPE },
  ) {
    return this.governmentsService.getMinistriesOfGovernment(
      +params.id,
      params.level,
    );
  }
}
