import {
  Injectable,
  BadRequestException,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { POLL_TYPE, RESOURCE_TYPE } from '@prisma/client';
import { PrismaService } from 'src/prisma.service';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class PollsService {
  constructor(private readonly prisma: PrismaService) {}

  async createPoll(data: {
    question: string;
    title: string;
    description?: string;
    image?: string;
    type: POLL_TYPE;
    deadline: Date;
    createdByUserId?: number;
    createdBySystem?: boolean;
    resourceType?: RESOURCE_TYPE;
    resourceId?: number;
    options: {
      text: string;
      resourceId?: number;
      resourceType?: RESOURCE_TYPE;
    }[];
  }) {
    const poll = await this.prisma.polls.create({
      data: {
        code: uuidv4(),
        question: data.question,
        title: data.title,
        description: data.description,
        image: data.image,
        type: data.type,
        deadline: data.deadline,
        createdByUser: data.createdByUserId,
        createdBy: data.createdBySystem ? 'SYSTEM' : 'USER',
        resourceType: data.resourceType,
        resourceId: data.resourceId,
        hash: uuidv4(),
        options: {
          create: data.options.map((opt) => ({
            text: opt.text,
            resourceId: opt.resourceId,
            resourceType: opt.resourceType,
          })),
        },
      },
    });

    return poll;
  }

  async getPoll(id: number) {
    const poll = await this.prisma.polls.findUnique({
      where: { id },
      include: {
        options: {
          include: {
            responses: true,
          },
        },
      },
    });

    if (!poll) throw new NotFoundException('Poll not found');

    const now = new Date();
    const expired = poll.deadline < now;

    return {
      ...poll,
      expired,
      totalVotes: poll.options.reduce(
        (acc, opt) => acc + opt.responses.length,
        0,
      ),
      options: poll.options.map((opt) => ({
        id: opt.id,
        text: opt.text,
        resourceId: opt.resourceId,
        resourceType: opt.resourceType,
        votes: opt.responses.length,
      })),
    };
  }

  async submitResponse(data: {
    pollId: number;
    userId?: number;
    anonymousId?: string;
    optionIds: number[];
  }) {
    if (!data.userId && !data.anonymousId) {
      throw new ForbiddenException('Authentication required to vote');
    }

    const poll = await this.prisma.polls.findUnique({
      where: { id: data.pollId },
      include: { options: true },
    });
    if (!poll) throw new NotFoundException('Poll not found');
    if (poll.deadline && poll.deadline < new Date())
      throw new BadRequestException('Poll expired');

    const whereClause = {
      pollId: data.pollId,
      ...(data.userId ? { userId: data.userId } : {}),
      ...(data.anonymousId ? { anonymousId: data.anonymousId } : {}),
    };

    // Delete existing responses for both RADIO and CHECKBOX polls to allow vote changes
    await this.prisma.poll_responses.deleteMany({ where: whereClause });

    if (poll.type === 'RADIO' && data.optionIds.length !== 1) {
      throw new BadRequestException(
        'Only one option is allowed in single-select poll',
      );
    }

    await this.prisma.poll_responses.createMany({
      data: data.optionIds.map((optionId) => ({
        pollId: data.pollId,
        optionId,
        userId: data.userId,
        anonymousId: data.anonymousId,
      })),
    });

    return { message: 'Vote submitted' };
  }

  async getPollsForResource(resourceType: RESOURCE_TYPE, resourceId: number) {
    return this.prisma.polls.findMany({
      where: { resourceType, resourceId },
      orderBy: { createdAt: 'desc' },
      include: {
        options: {
          include: {
            responses: true,
          },
        },
      },
    });
  }

  async cleanupExpiredPolls() {
    const now = new Date();
    return this.prisma.polls.deleteMany({
      where: {
        deadline: {
          lt: now,
        },
        responses: {
          none: {},
        },
      },
    });
  }
}
